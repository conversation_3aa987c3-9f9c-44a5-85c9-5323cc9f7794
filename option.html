<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>AI QA - Select Test Type</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: "#6366F1", // Indigo 500
                        "background-light": "#F8FAFC", // Slate 50
                        "background-dark": "#1E293B", // Slate 800
                        "card-light": "#FFFFFF",
                        "card-dark": "#334155", // Slate 700
                        "text-light": "#1E293B", // Slate 800
                        "text-dark": "#E2E8F0", // Slate 200
                        "subtext-light": "#64748B", // Slate 500
                        "subtext-dark": "#94A3B8", // Slate 400
                        "border-light": "#E2E8F0", // Slate 200
                        "border-dark": "#475569", // Slate 600
                    },
                    fontFamily: {
                        display: ["Inter", "sans-serif"],
                    },
                    borderRadius: {
                        DEFAULT: "0.5rem",
                    },
                },
            },
        };
    </script>
<style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display text-text-light dark:text-text-dark">
<div class="flex h-screen">
<aside class="w-64 flex flex-col bg-white dark:bg-card-dark border-r border-border-light dark:border-border-dark">
<div class="flex items-center justify-center h-16 border-b border-border-light dark:border-border-dark">
<div class="bg-primary p-2 rounded-lg">
<span class="material-icons text-white">smart_toy</span>
</div>
<h1 class="ml-3 text-xl font-bold text-text-light dark:text-text-dark">AI QA</h1>
</div>
<nav class="flex-1 px-4 py-4">
<h2 class="px-4 text-xs font-semibold text-subtext-light dark:text-subtext-dark tracking-wider uppercase">Projects</h2>
<ul class="mt-2 space-y-1">
<li>
<a class="flex items-center px-4 py-2 text-subtext-light dark:text-subtext-dark hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md" href="#">
<span class="material-icons mr-3">folder</span>
                            Project Alpha
                        </a>
</li>
<li>
<a class="flex items-center px-4 py-2 text-subtext-light dark:text-subtext-dark hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md" href="#">
<span class="material-icons mr-3">folder</span>
                            Project Beta
                        </a>
</li>
<li>
<a class="flex items-center px-4 py-2 text-primary bg-indigo-100 dark:bg-primary/20 dark:text-indigo-300 rounded-md" href="#">
<span class="material-icons mr-3">folder</span>
                            Project Gamma
                        </a>
</li>
</ul>
</nav>
<div class="p-4 border-t border-border-light dark:border-border-dark">
<button class="w-full bg-primary text-white py-2 px-4 rounded-md flex items-center justify-center hover:bg-indigo-700">
<span class="material-icons mr-2">add</span>
                    New Project
                </button>
</div>
</aside>
<main class="flex-1 flex flex-col">
<header class="flex items-center justify-between h-16 px-8 border-b border-border-light dark:border-border-dark bg-white dark:bg-card-dark">
<h1 class="text-2xl font-semibold text-text-light dark:text-text-dark">Welcome</h1>
<div class="flex items-center space-x-4">
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">search</span>
</button>
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">notifications_none</span>
</button>
<button>
<img alt="User avatar" class="w-8 h-8 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCPXlOIUoxBTm9JnlTN6Xb7NbTMDthOSjG3Ikoe9PSsFkRpiWcxZuY3HlJH5Y4YBT91bDqqL34t414vSnPEH7Wzim5cRNPHnN4654AHHlb4jZcnAyhTNs6_lsxDNZR9tJ08S96p0gNaks91Bc7gh6IUhxRCQEyAleY2CpHjNlziV_n6JV1gUdTVTswfyL-qCqV2-qo_Ure7WFQqCFsJuhCVUyWpf7ggwmHIARRZ7XPlcKvfREX1za-vDLxWux_TVLt8K-sW-WaipMHl"/>
</button>
</div>
</header>
<div class="flex-1 flex items-center justify-center p-8 bg-background-light dark:bg-background-dark">
<div class="w-full max-w-2xl bg-card-light dark:bg-card-dark p-8 rounded-lg shadow-md">
<h2 class="text-2xl font-bold text-text-light dark:text-text-dark mb-2">Select Testing Type for "Project Gamma"</h2>
<p class="text-subtext-light dark:text-subtext-dark mb-8">Choose the type of automated testing you want to set up.</p>
<form>
<div class="space-y-4">
<label class="flex items-center p-4 border border-border-light dark:border-border-dark rounded-lg cursor-pointer has-[:checked]:bg-indigo-50 dark:has-[:checked]:bg-primary/20 has-[:checked]:border-primary dark:has-[:checked]:border-primary transition-all" for="api-testing">
<input checked="" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-500 bg-gray-100 dark:bg-gray-600" id="api-testing" name="test-type" type="radio" value="api"/>
<div class="ml-4">
<h3 class="font-semibold text-text-light dark:text-text-dark">API Auto-Testing</h3>
<p class="text-sm text-subtext-light dark:text-subtext-dark">Automate tests for your application's API endpoints.</p>
</div>
<span class="material-icons ml-auto text-primary">api</span>
</label>
<div class="relative">
<label class="flex items-center p-4 border border-border-light dark:border-border-dark rounded-lg cursor-not-allowed bg-gray-100 dark:bg-gray-800/50 opacity-50" for="ui-testing">
<input class="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-500" disabled="" id="ui-testing" name="test-type" type="radio" value="ui"/>
<div class="ml-4">
<h3 class="font-semibold text-text-light dark:text-text-dark">UI Auto-Testing</h3>
<p class="text-sm text-subtext-light dark:text-subtext-dark">Automate user interface tests for web applications.</p>
</div>
<span class="material-icons ml-auto text-subtext-light dark:text-subtext-dark">web</span>
</label>
<div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-3 py-1 bg-yellow-100 dark:bg-yellow-900/80 text-yellow-800 dark:text-yellow-300 text-xs font-semibold rounded-full">
                                    Future Phase
                                </div>
</div>
</div>
<div class="mt-8 flex justify-end">
<a href="/integration.html" class="bg-primary text-white py-2 px-6 rounded-md font-semibold hover:bg-indigo-700 transition-colors" type="submit">
                                Submit
                            </a>
</div>
</form>
</div>
</div>
</main>
</div>

</body></html>