<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>AI QA - API Test Details</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: "#6366F1",
                        "background-light": "#F8FAFC",
                        "background-dark": "#1E293B",
                        "surface-light": "#FFFFFF",
                        "surface-dark": "#334155",
                        "text-light": "#0F172A",
                        "text-dark": "#F8FAFC",
                        "subtext-light": "#64748B",
                        "subtext-dark": "#94A3B8",
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                    borderRadius: {
                        DEFAULT: "0.5rem",
                    },
                },
            },
        };
    </script>
<style>
        .material-icons {
            font-size: inherit;
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark font-sans antialiased">
<div class="flex h-screen">
<aside class="w-64 bg-surface-light dark:bg-surface-dark flex flex-col border-r border-slate-200 dark:border-slate-700">
<div class="h-16 flex items-center px-6 border-b border-slate-200 dark:border-slate-700">
<div class="bg-primary p-2 rounded-lg">
<span class="material-icons text-white">auto_awesome</span>
</div>
<h1 class="ml-3 text-xl font-bold">AI QA</h1>
</div>
<nav class="flex-1 p-4 space-y-2">
<span class="px-4 text-sm font-semibold text-subtext-light dark:text-subtext-dark">PROJECTS</span>
<a class="flex items-center px-4 py-2 rounded-lg text-subtext-light dark:text-subtext-dark hover:bg-slate-100 dark:hover:bg-slate-700" href="#">
<span class="material-icons mr-3">folder</span>
<span>Project Alpha</span>
</a>
<a class="flex items-center px-4 py-2 rounded-lg text-subtext-light dark:text-subtext-dark hover:bg-slate-100 dark:hover:bg-slate-700" href="#">
<span class="material-icons mr-3">folder</span>
<span>Project Beta</span>
</a>
<a class="flex items-center px-4 py-2 rounded-lg text-white bg-primary" href="#">
<span class="material-icons mr-3">folder</span>
<span>Project Gamma</span>
</a>
</nav>
<div class="p-4 border-t border-slate-200 dark:border-slate-700">
<button class="w-full flex items-center justify-center bg-primary text-white py-2 px-4 rounded-lg hover:bg-indigo-700">
<span class="material-icons mr-2">add</span>
                    New Project
                </button>
</div>
</aside>
<main class="flex-1 flex flex-col">
<header class="bg-surface-light dark:bg-surface-dark border-b border-slate-200 dark:border-slate-700">
<div class="h-16 flex items-center justify-between px-6">
<div class="flex items-center space-x-4">
<button class="flex items-center space-x-2 text-sm font-semibold text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">arrow_back_ios_new</span>
<span>API List</span>
</button>
<span class="text-subtext-light dark:text-subtext-dark">/</span>
<span class="text-sm font-semibold text-text-light dark:text-text-dark">/v1/users</span>
</div>
<div class="flex items-center space-x-4">
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">help_outline</span>
</button>
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">notifications_none</span>
</button>
<div class="w-8 h-8 bg-slate-200 rounded-full overflow-hidden">
<img alt="User avatar" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBlGCRNJZN96h2vt6AEXDji_Gg32MmSsGNDofhTj8rXERpVLWjdVF1HTJwhTMLhNeznxinVGFzHGWrt4izytsHqq5IVpnzwCoyccdlCyEGFkzfWyKjj6EaH2zRdZkqZcYnbjDJOns2f2lMy48lRTDFg_Li0QpcGRire8z7wZ-0df9O0H8eJBJNBmUQxJwFtsrQnJUoTHlfhElr4P1K_mvVGlFpDLzLdlb89pMJNUSM_XtG76FeK-dmoZ_jjmve-J_E91Ku-jTFxBbE"/>
</div>
</div>
</div>
<div class="px-6 border-t border-slate-200 dark:border-slate-700">
<nav class="flex space-x-8">
<a href="/apitest.html" class="py-4 px-1 border-b-2 border-primary text-primary font-semibold text-sm" href="#">API Test</a>
<a href="/apitestflow.html" class="py-4 px-1 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-600 hover:text-text-light dark:hover:text-text-dark font-medium text-sm" href="#">API Flow Test</a>
<a href="/integration.html" class="py-4 px-1 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-600 hover:text-text-light dark:hover:text-text-dark font-medium text-sm" href="#">Integration</a>
<a class="py-4 px-1 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-600 hover:text-text-light dark:hover:text-text-dark font-medium text-sm" href="#">Results</a>
</nav>
</div>
</header>
<div class="flex-1 p-6 overflow-y-auto">
<div class="bg-surface-light dark:bg-surface-dark p-6 rounded-lg shadow-sm">
<div class="space-y-8">
<div class="flex justify-between items-center">
<div class="flex items-center space-x-4">
<h2 class="text-2xl font-bold text-text-light dark:text-text-dark">/v1/users</h2>
<span class="px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">GET</span>
<div class="flex items-center space-x-2 text-green-600 dark:text-green-400">
<span class="material-icons text-xl">check_circle</span>
<span class="font-medium">200 OK</span>
</div>
</div>
<button class="bg-primary text-white py-2 px-4 rounded-lg flex items-center hover:bg-indigo-700">
<span class="material-icons mr-2">play_arrow</span>
                                Run Test
                            </button>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
<div class="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
<h3 class="text-sm font-medium text-subtext-light dark:text-subtext-dark">Timeout</h3>
<p class="text-lg font-semibold text-text-light dark:text-text-dark">30s</p>
</div>
<div class="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
<h3 class="text-sm font-medium text-subtext-light dark:text-subtext-dark">Rate Limit</h3>
<p class="text-lg font-semibold text-text-light dark:text-text-dark">100/min</p>
</div>
<div class="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
<h3 class="text-sm font-medium text-subtext-light dark:text-subtext-dark">Method</h3>
<p class="text-lg font-semibold text-text-light dark:text-text-dark">GET</p>
</div>
<div class="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
<h3 class="text-sm font-medium text-subtext-light dark:text-subtext-dark">API URL</h3>
<p class="text-lg font-semibold text-text-light dark:text-text-dark truncate">/api/v1/users</p>
</div>
</div>
<div>
<h3 class="text-lg font-semibold mb-2">Documentation View</h3>
<div class="prose prose-slate dark:prose-invert max-w-none p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
<h4>Get User List</h4>
<p>This endpoint retrieves a list of all users in the system. The response is paginated, and you can control the page size and number using query parameters.</p>
<h5>Parameters:</h5>
<ul>
<li><code>page</code> (optional, integer): The page number to retrieve. Defaults to 1.</li>
<li><code>limit</code> (optional, integer): The number of users per page. Defaults to 20.</li>
</ul>
</div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<div>
<h3 class="text-lg font-semibold mb-2">Headers</h3>
<div class="bg-slate-800 text-slate-200 p-4 rounded-lg font-mono text-sm overflow-x-auto">
<pre><code>{
    "Content-Type": "application/json",
    "Authorization": "Bearer {your_token}"
}</code></pre>
</div>
</div>
<div>
<h3 class="text-lg font-semibold mb-2">Request Body</h3>
<div class="bg-slate-800 text-slate-200 p-4 rounded-lg font-mono text-sm overflow-x-auto">
<pre><code>{
    "filter": {
        "status": "active"
    },
    "sort": {
        "field": "createdAt",
        "order": "desc"
    }
}</code></pre>
</div>
</div>
</div>
<div>
<h3 class="text-lg font-semibold mb-2">Expected Response</h3>
<div class="bg-slate-800 text-slate-200 p-4 rounded-lg font-mono text-sm overflow-x-auto">
<pre><code>{
    "data": [
        {
            "id": "usr_123",
            "name": "John Doe",
            "email": "<EMAIL>",
            "status": "active"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 20,
        "total": 150
    }
}</code></pre>
</div>
</div>
<div>
<h3 class="text-lg font-semibold mb-2">Result History</h3>
<div class="overflow-x-auto">
<table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
<thead class="bg-slate-50 dark:bg-slate-700">
<tr>
<th class="px-6 py-3 text-left text-xs font-medium text-subtext-light dark:text-subtext-dark uppercase tracking-wider" scope="col">Timestamp</th>
<th class="px-6 py-3 text-left text-xs font-medium text-subtext-light dark:text-subtext-dark uppercase tracking-wider" scope="col">Status</th>
<th class="px-6 py-3 text-left text-xs font-medium text-subtext-light dark:text-subtext-dark uppercase tracking-wider" scope="col">Duration</th>
<th class="px-6 py-3 text-left text-xs font-medium text-subtext-light dark:text-subtext-dark uppercase tracking-wider" scope="col">Assertions</th>
</tr>
</thead>
<tbody class="bg-surface-light dark:bg-surface-dark divide-y divide-slate-200 dark:divide-slate-700">
<tr>
<td class="px-6 py-4 whitespace-nowrap text-sm text-text-light dark:text-text-dark">2023-10-27 10:30:15 UTC</td>
<td class="px-6 py-4 whitespace-nowrap text-sm">
<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">200 OK</span>
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-subtext-light dark:text-subtext-dark">150ms</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-subtext-light dark:text-subtext-dark">3/3 passed</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-sm text-text-light dark:text-text-dark">2023-10-27 09:15:42 UTC</td>
<td class="px-6 py-4 whitespace-nowrap text-sm">
<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">200 OK</span>
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-subtext-light dark:text-subtext-dark">145ms</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-subtext-light dark:text-subtext-dark">3/3 passed</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-sm text-text-light dark:text-text-dark">2023-10-26 18:05:01 UTC</td>
<td class="px-6 py-4 whitespace-nowrap text-sm">
<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">500 Internal Server Error</span>
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-subtext-light dark:text-subtext-dark">2500ms</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-subtext-light dark:text-subtext-dark">1/3 passed</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
</div>
</div>
</main>
</div>

</body></html>