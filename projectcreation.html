<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>AI QA - Add Project</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: "#6366F1",
                        "background-light": "#F9FAFB",
                        "background-dark": "#111827",
                        "card-light": "#FFFFFF",
                        "card-dark": "#1F2937",
                        "text-light": "#111827",
                        "text-dark": "#F9FAFB",
                        "subtext-light": "#6B7280",
                        "subtext-dark": "#9CA3AF",
                        "border-light": "#E5E7EB",
                        "border-dark": "#374151"
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"],
                    },
                    borderRadius: {
                        DEFAULT: "0.5rem",
                    },
                },
            },
        };
    </script>
<style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark transition-colors duration-300">
<div id="root">
<main class="flex h-screen">
<aside class="w-64 bg-card-light dark:bg-card-dark border-r border-border-light dark:border-border-dark flex flex-col justify-between">
<div>
<div class="p-4 border-b border-border-light dark:border-border-dark flex items-center gap-2">
<div class="bg-primary p-2 rounded-lg">
<span class="material-icons text-white">auto_awesome</span>
</div>
<h1 class="text-xl font-bold text-text-light dark:text-text-dark">AI QA</h1>
</div>
<div class="p-4">
<h2 class="text-xs font-semibold text-subtext-light dark:text-subtext-dark uppercase tracking-wider mb-4">Projects</h2>
<ul>
<li class="mb-2">
<a class="flex items-center p-2 rounded text-subtext-light dark:text-subtext-dark hover:bg-gray-100 dark:hover:bg-gray-700" href="#">
<span class="material-icons mr-3">folder_open</span>
                                    Project Alpha
                                </a>
</li>
<li class="mb-2">
<a class="flex items-center p-2 rounded text-subtext-light dark:text-subtext-dark hover:bg-gray-100 dark:hover:bg-gray-700" href="#">
<span class="material-icons mr-3">folder_open</span>
                                    Project Beta
                                </a>
</li>
<li>
<a class="flex items-center p-2 rounded bg-primary/10 text-primary" href="#">
<span class="material-icons mr-3">folder_open</span>
                                    Project Gamma
                                </a>
</li>
</ul>
</div>
</div>
<div class="p-4 border-t border-border-light dark:border-border-dark">
<button class="w-full bg-primary text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-primary/90 transition-colors" onclick="showView('addProjectView')">
<span class="material-icons">add</span>
                        New Project
                    </button>
</div>
</aside>
<div class="flex-1 flex flex-col">
<header class="h-16 flex items-center justify-between px-8 border-b border-border-light dark:border-border-dark">
<h1 class="text-xl font-semibold text-text-light dark:text-text-dark">Welcome</h1>
<div class="flex items-center gap-4">
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">search</span>
</button>
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">notifications</span>
</button>
<div class="w-8 h-8 rounded-full bg-subtext-light dark:bg-subtext-dark">
<img alt="User avatar" class="rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAt7glUK66wgWA5gOUJ3De1LIrpsui6iW7ny97p0a_MWfaRFcZYVz4aD3FhP8mqZUX-ioZfE_eYjwXOFvOooq7S6LWQaLlOumXF1Bs8sCn_o6tHqvvvaonTpVxmAA9KOFRjQ_0f1XaPh0azSupNO26qu1PPreLqzNf63DUmPHUjhoqxlCXop0g9Y1Lhx7neSWAufbMHbfEPmG1uNoVSksuYqqpMIL8_Y33Runhpq7GuMU9TvR0DPjWDHz4xklItMpkWCFTkqpGB0z0q"/>
</div>
</div>
</header>
<nav class="hidden px-8 border-b border-border-light dark:border-border-dark" id="secondaryNav">
<div class="flex items-center -mb-px">
<a class="nav-item py-4 px-4 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-700 hover:text-text-light dark:hover:text-text-dark font-medium" href="#" id="nav-api-test">API Test</a>
<a class="nav-item py-4 px-4 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-700 hover:text-text-light dark:hover:text-text-dark font-medium" href="#" id="nav-api-flow-test">API Flow Test</a>
<a class="nav-item py-4 px-1 border-b-2 border-primary text-primary font-semibold" href="#" id="nav-integration">Integration</a>
<a class="nav-item py-4 px-4 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-700 hover:text-text-light dark:hover:text-text-dark font-medium" href="#" id="nav-results">Results</a>
</div>
</nav>
<div class="flex-1 overflow-y-auto p-8">
<div class="hidden max-w-2xl mx-auto" id="addProjectView">
<div class="bg-card-light dark:bg-card-dark p-8 rounded-lg shadow-md">
<h2 class="text-2xl font-bold mb-2 text-text-light dark:text-text-dark">Create a New Project</h2>
<p class="text-subtext-light dark:text-subtext-dark mb-6">Let's get started by providing some basic information.</p>
<form id="addProjectForm">
<div class="mb-4">
<label class="block text-sm font-medium text-subtext-light dark:text-subtext-dark mb-1" for="projectName">Project Name <span class="text-red-500">*</span></label>
<input class="w-full px-3 py-2 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary" id="projectName" name="projectName" required="" type="text"/>
</div>
<div class="mb-4">
<label class="block text-sm font-medium text-subtext-light dark:text-subtext-dark mb-1" for="baseUrl">Base URL <span class="text-red-500">*</span></label>
<input class="w-full px-3 py-2 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary" id="baseUrl" name="baseUrl" placeholder="https://api.example.com" required="" type="url"/>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-subtext-light dark:text-subtext-dark mb-1" for="description">Description</label>
<textarea class="w-full px-3 py-2 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary" id="description" name="description" rows="3"></textarea>
</div>
<div class="flex justify-end">
<a href="/option.html" class="bg-primary text-white py-2 px-6 rounded-lg hover:bg-primary/90 transition-colors" type="submit">Submit</a>
</div>
</form>
</div>
</div>
<div class="hidden max-w-2xl mx-auto" id="selectTestTypeView">
<div class="bg-card-light dark:bg-card-dark p-8 rounded-lg shadow-md">
<h2 class="text-2xl font-bold mb-2 text-text-light dark:text-text-dark">Select Testing Type</h2>
<p class="text-subtext-light dark:text-subtext-dark mb-6">Choose the type of automated testing you want to set up for this project.</p>
<form id="selectTestTypeForm">
<div class="space-y-4">
<label class="flex items-center p-4 border border-border-light dark:border-border-dark rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 has-[:checked]:border-primary has-[:checked]:ring-2 has-[:checked]:ring-primary" for="api-testing">
<input checked="" class="h-4 w-4 text-primary focus:ring-primary border-gray-300" id="api-testing" name="testType" type="radio" value="api"/>
<div class="ml-4">
<h3 class="font-semibold text-text-light dark:text-text-dark">API Auto-Testing</h3>
<p class="text-sm text-subtext-light dark:text-subtext-dark">Automated REST API testing with intelligent validation.</p>
</div>
</label>
<label class="relative flex items-center p-4 border border-border-light dark:border-border-dark rounded-lg cursor-not-allowed opacity-50" for="ui-testing">
<input class="h-4 w-4 text-primary focus:ring-primary border-gray-300" disabled="" id="ui-testing" name="testType" type="radio" value="ui"/>
<div class="ml-4">
<h3 class="font-semibold text-text-light dark:text-text-dark">UI Auto-Testing</h3>
<p class="text-sm text-subtext-light dark:text-subtext-dark">Comprehensive user interface and workflow testing.</p>
</div>
<span class="absolute top-2 right-2 text-xs bg-gray-200 dark:bg-gray-600 text-subtext-light dark:text-subtext-dark px-2 py-1 rounded-full">Future</span>
</label>
</div>
<div class="flex justify-end mt-6">
<button class="bg-primary text-white py-2 px-6 rounded-lg hover:bg-primary/90 transition-colors" type="submit">Submit</button>
</div>
</form>
</div>
</div>
<div class="hidden max-w-4xl mx-auto" id="integrationView">
<h2 class="text-2xl font-bold mb-2 text-text-light dark:text-text-dark">Integrations</h2>
<p class="text-subtext-light dark:text-subtext-dark mb-6">Connect your project to external sources for data scraping and analysis.</p>
<div class="bg-card-light dark:bg-card-dark p-8 rounded-lg shadow-md">
<form id="integrationForm">
<div class="space-y-6">
<div>
<label class="block text-sm font-medium text-subtext-light dark:text-subtext-dark mb-1" for="repoUrl">Repository URL</label>
<div class="flex items-center">
<span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-border-light dark:border-border-dark bg-gray-50 dark:bg-gray-700 text-subtext-light dark:text-subtext-dark">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path clip-rule="evenodd" d="M10 0C4.477 0 0 4.477 0 10c0 4.418 2.865 8.166 6.839 9.489.5.092.682-.218.682-.484 0-.238-.009-.868-.014-1.703-2.782.604-3.369-1.342-3.369-1.342-.454-1.157-1.11-1.465-1.11-1.465-.908-.62.069-.608.069-.608 1.003.07 1.531 1.03 1.531 1.03.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.112-4.555-4.943 0-1.091.39-1.984 1.031-2.682-.103-.253-.446-1.27.098-2.646 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 5.424c.85.004 1.705.114 2.504.336 1.909-1.296 2.747-1.027 2.747-1.027.546 1.375.202 2.393.1 2.646.64.698 1.03 1.59 1.03 2.682 0 3.84-2.338 4.687-4.566 4.935.359.307.678.915.678 1.846 0 1.332-.012 2.407-.012 2.734 0 .268.18.579.688.482A10.001 10.001 0 0020 10c0-5.523-4.477-10-10-10z" fill-rule="evenodd"></path></svg>
</span>
<input class="flex-1 block w-full rounded-none rounded-r-md px-3 py-2 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark focus:outline-none focus:ring-2 focus:ring-primary" id="repoUrl" name="repoUrl" placeholder="https://github.com/user/repo" type="url"/>
</div>
</div>
<div>
<label class="block text-sm font-medium text-subtext-light dark:text-subtext-dark mb-1" for="confluenceUrl">Confluence URL</label>
<input class="w-full px-3 py-2 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary" id="confluenceUrl" name="confluenceUrl" placeholder="https://company.atlassian.net/wiki" type="url"/>
</div>
<div>
<label class="block text-sm font-medium text-subtext-light dark:text-subtext-dark mb-1" for="jiraUrl">Jira URL</label>
<input class="w-full px-3 py-2 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary" id="jiraUrl" name="jiraUrl" placeholder="https://company.atlassian.net" type="url"/>
</div>
<div>
<label class="block text-sm font-medium text-subtext-light dark:text-subtext-dark mb-1" for="figmaUrl">Figma URL</label>
<input class="w-full px-3 py-2 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary" id="figmaUrl" name="figmaUrl" placeholder="https://figma.com/file/..." type="url"/>
</div>
<div>
<label class="block text-sm font-medium text-subtext-light dark:text-subtext-dark mb-1" for="otherUrl">Other Site for Scraping</label>
<input class="w-full px-3 py-2 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary" id="otherUrl" name="otherUrl" placeholder="e.g., API documentation site" type="url"/>
</div>
</div>
<div class="flex justify-end mt-8">
<button class="bg-primary text-white py-2 px-6 rounded-lg hover:bg-primary/90 transition-colors" type="submit">Save Integrations</button>
</div>
</form>
</div>
</div>
</div>
</div>
</main>
</div>
<script>
        const views = ['addProjectView', 'selectTestTypeView', 'integrationView'];
        const secondaryNav = document.getElementById('secondaryNav');
        const navItems = document.querySelectorAll('.nav-item');
        function showView(viewId, showNav = false) {
            views.forEach(id => {
                const element = document.getElementById(id);
                if (id === viewId) {
                    element.classList.remove('hidden');
                } else {
                    element.classList.add('hidden');
                }
            });
            if (showNav) {
                secondaryNav.classList.remove('hidden');
                secondaryNav.classList.add('flex');
            } else {
                secondaryNav.classList.add('hidden');
                secondaryNav.classList.remove('flex');
            }
        }
        document.getElementById('addProjectForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // Add validation logic here if needed
            showView('selectTestTypeView');
        });
        document.getElementById('selectTestTypeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            showView('integrationView', true);
            updateActiveNav('nav-integration');
        });
        document.getElementById('integrationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // Logic to save integrations
            console.log('Integrations saved');
        });
        navItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                updateActiveNav(this.id);
                // Here you would typically also switch the view based on the nav item clicked
                if(this.id === 'nav-integration') {
                    showView('integrationView', true);
                } else {
                    // Hide integration view and show other relevant views if needed
                    // For now, let's just keep the nav active
                    showView('integrationView', true); // Or another view
                }
            });
        });
        function updateActiveNav(activeId) {
            navItems.forEach(item => {
                item.classList.remove('border-primary', 'text-primary', 'font-semibold');
                item.classList.add('border-transparent', 'text-subtext-light', 'dark:text-subtext-dark', 'font-medium', 'hover:border-gray-300', 'dark:hover:border-gray-700', 'hover:text-text-light', 'dark:hover:text-text-dark');
            });
            const activeItem = document.getElementById(activeId);
            activeItem.classList.add('border-primary', 'text-primary', 'font-semibold');
            activeItem.classList.remove('border-transparent', 'text-subtext-light', 'dark:text-subtext-dark', 'font-medium', 'hover:border-gray-300', 'dark:hover:border-gray-700', 'hover:text-text-light', 'dark:hover:text-text-dark');
        }
        // Initially show the 'Add Project' view for this flow
        showView('addProjectView');
    </script>

</body></html>