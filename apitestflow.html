<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>AI QA</title>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        'background-light': '#F8F9FA',
                        'background-dark': '#111827',
                        'surface-light': '#FFFFFF',
                        'surface-dark': '#1F2937',
                        'text-light': '#1F2937',
                        'text-dark': '#F9FAFB',
                        'subtle-light': '#6B7280',
                        'subtle-dark': '#9CA3AF',
                        'border-light': '#E5E7EB',
                        'border-dark': '#374151',
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                    borderRadius: {
                        'DEFAULT': '0.5rem',
                        'lg': '0.75rem',
                    },
                },
            },
        };
    </script>
<style>
        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 24px;display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark font-sans">
<div class="flex h-screen">
<aside class="w-64 bg-surface-light dark:bg-surface-dark flex flex-col border-r border-border-light dark:border-border-dark">
<div class="flex items-center justify-start p-4 h-16 border-b border-border-light dark:border-border-dark">
<div class="bg-primary p-2 rounded-lg mr-3">
<span class="material-icons text-white">auto_awesome</span>
</div>
<h1 class="text-xl font-bold text-text-light dark:text-text-dark">AI QA</h1>
</div>
<div class="flex-grow p-4">
<h2 class="text-xs font-semibold text-subtle-light dark:text-subtle-dark uppercase tracking-wider mb-3">Projects</h2>
<nav class="space-y-1">
<a class="flex items-center px-3 py-2 text-sm font-medium rounded text-text-light dark:text-text-dark hover:bg-gray-100 dark:hover:bg-gray-700" href="#">
<span class="material-icons text-subtle-light dark:text-subtle-dark mr-3">folder</span>
<span class="truncate">Project Alpha</span>
</a>
<a class="flex items-center px-3 py-2 text-sm font-medium rounded text-text-light dark:text-text-dark hover:bg-gray-100 dark:hover:bg-gray-700" href="#">
<span class="material-icons text-subtle-light dark:text-subtle-dark mr-3">folder</span>
<span class="truncate">Project Beta</span>
</a>
<a class="flex items-center px-3 py-2 text-sm font-medium rounded bg-primary/10 dark:bg-primary/20 text-primary" href="#">
<span class="material-icons text-primary mr-3">folder</span>
<span class="truncate">Project Gamma</span>
</a>
</nav>
</div>
<div class="p-4 border-t border-border-light dark:border-border-dark">
<button class="w-full flex items-center justify-center bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors">
<span class="material-icons mr-2">add</span>
                    New Project
                </button>
</div>
</aside>
<main class="flex-1 flex flex-col">
<header class="flex items-center justify-end p-4 h-16 border-b border-border-light dark:border-border-dark bg-surface-light dark:bg-surface-dark">
<div class="flex items-center space-x-6">
<button class="text-subtle-light dark:text-subtle-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">help_outline</span>
</button>
<button class="relative text-subtle-light dark:text-subtle-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">notifications</span>
<span class="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full"></span>
</button>
<button>
<img alt="User avatar" class="rounded-full w-8 h-8" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDAJQxVRAYQA-ltZ4P0iK0WT_c65QonQKQ5gQmJ995sHSOLBCHSO4URrMk8awRjTxfttJ4IIRjo6e579d40DtFcIqG0VA81WNRkK6462JFTHZMtx_tZaEnJx5PcOwabM524ZyJw0_Achg1s_X33Gzcwuj5utOAFo4xcW_zegjpy_cpqk0Xo0cMo0qABYGl8gIw1UiSg2aXIovLLmFO5ifrkRFeipRV6L5h88hgE96xLgiEEVvTeiEBMbJ8GvgH5SU0R7I3wmVT7fyU"/>
</button>
</div>
</header>
<div class="px-6 bg-surface-light dark:bg-surface-dark border-b border-border-light dark:border-border-dark">
<div class="flex space-x-8">
<a href="/apitest.html" class="py-3 text-sm font-medium text-subtle-light dark:text-subtle-dark hover:text-text-light dark:hover:text-text-dark">API Test</a>
<a href="/apitestflow.html" class="py-3 text-sm font-medium border-b-2 border-primary text-primary">API Flow Test</a>
<a href="/integration.html" class="py-3 text-sm font-medium text-subtle-light dark:text-subtle-dark hover:text-text-light dark:hover:text-text-dark">Integration</a>
<a class="py-3 text-sm font-medium text-subtle-light dark:text-subtle-dark hover:text-text-light dark:hover:text-text-dark">Results</a>
</div>
</div>
<div class="flex-grow flex p-6 space-x-6">
<div class="w-64 bg-surface-light dark:bg-surface-dark rounded-lg border border-border-light dark:border-border-dark p-4 flex flex-col">
<h3 class="text-lg font-semibold mb-4 text-text-light dark:text-text-dark">Nodes</h3>
<div class="space-y-3 flex-grow overflow-y-auto">
<div class="p-3 border border-border-light dark:border-border-dark rounded-lg bg-background-light dark:bg-background-dark cursor-pointer hover:border-primary">
<div class="flex items-center mb-1">
<span class="material-icons text-green-500 text-base mr-2">description</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">CSV Read</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark">Read data from a CSV file.</p>
</div>
<div class="p-3 border border-border-light dark:border-border-dark rounded-lg bg-background-light dark:bg-background-dark cursor-pointer hover:border-primary">
<div class="flex items-center mb-1">
<span class="material-icons text-blue-500 text-base mr-2">sync</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">For Loop</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark">Iterate over a list of items.</p>
</div>
<div class="p-3 border border-border-light dark:border-border-dark rounded-lg bg-background-light dark:bg-background-dark cursor-pointer hover:border-primary">
<div class="flex items-center mb-1">
<span class="material-icons text-purple-500 text-base mr-2">http</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">HTTP Request</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark">Make an HTTP request.</p>
</div>
<div class="p-3 border border-border-light dark:border-border-dark rounded-lg bg-background-light dark:bg-background-dark cursor-pointer hover:border-primary">
<div class="flex items-center mb-1">
<span class="material-icons text-orange-500 text-base mr-2">timer</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">Waiting</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark">Wait for a specified duration.</p>
</div>
<div class="p-3 border border-border-light dark:border-border-dark rounded-lg bg-background-light dark:bg-background-dark cursor-pointer hover:border-primary">
<div class="flex items-center mb-1">
<span class="material-icons text-red-500 text-base mr-2">router</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">Openrouter</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark">Connect to Openrouter models.</p>
</div>
<div class="p-3 border border-border-light dark:border-border-dark rounded-lg bg-background-light dark:bg-background-dark cursor-pointer hover:border-primary">
<div class="flex items-center mb-1">
<span class="material-icons text-teal-500 text-base mr-2">save</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">Save CSV</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark">Save data to a CSV file.</p>
</div>
</div>
</div>
<div class="flex-1 flex flex-col bg-surface-light dark:bg-surface-dark rounded-lg border border-border-light dark:border-border-dark overflow-hidden">
<div class="flex items-center justify-between p-3 border-b border-border-light dark:border-border-dark flex-shrink-0">
<div class="flex items-center space-x-2">
<button class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
<span class="material-icons text-subtle-light dark:text-subtle-dark">dark_mode</span>
</button>
<button class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
<span class="material-icons text-subtle-light dark:text-subtle-dark">light_mode</span>
</button>
<div class="w-px h-6 bg-border-light dark:bg-border-dark"></div>
<button class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-sm font-medium text-subtle-light dark:text-subtle-dark">
<span class="material-icons text-sm">save</span>
<span>Save</span>
</button>
<button class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-sm font-medium text-subtle-light dark:text-subtle-dark">
<span class="material-icons text-sm">file_upload</span>
<span>Import</span>
</button>
<button class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-sm font-medium text-subtle-light dark:text-subtle-dark">
<span class="material-icons text-sm">file_download</span>
<span>Export</span>
</button>
</div>
<div class="flex items-center space-x-2">
<button class="flex items-center space-x-2 py-2 px-3 rounded-md bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-800 text-sm font-medium">
<span class="material-icons text-sm">play_arrow</span>
<span>Run</span>
</button>
<button class="flex items-center space-x-2 py-2 px-3 rounded-md bg-primary text-white hover:bg-primary/90 text-sm font-medium">
<span class="material-icons text-sm">fast_forward</span>
<span>Run All</span>
</button>
</div>
</div>
<div class="flex-grow bg-dots-pattern relative overflow-hidden">
<div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" id="canvas-content" style="transform-origin: center center;">
<div class="flex items-center justify-center">
<div class="w-40 bg-surface-light dark:bg-surface-dark rounded-lg shadow-md p-3 border border-border-light dark:border-border-dark relative flex-shrink-0">
<div class="flex items-center">
<span class="material-icons text-green-500 text-base mr-2">description</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">CSV Read</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark mt-1">Read data from a CSV file.</p>
<div class="absolute -right-2 top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary border-2 border-surface-light dark:border-surface-dark"></div>
</div>
<svg class="w-16 h-px text-primary flex-shrink-0" fill="none" viewBox="0 0 64 1" xmlns="http://www.w3.org/2000/svg">
<path d="M0 0.5H64" stroke="currentColor" stroke-width="2"></path>
</svg>
<div class="w-40 bg-surface-light dark:bg-surface-dark rounded-lg shadow-md p-3 border border-border-light dark:border-border-dark relative flex-shrink-0">
<div class="flex items-center">
<span class="material-icons text-blue-500 text-base mr-2">sync</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">For Loop</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark mt-1">Iterate over items.</p>
<div class="absolute -left-2 top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary border-2 border-surface-light dark:border-surface-dark"></div>
<div class="absolute -right-2 top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary border-2 border-surface-light dark:border-surface-dark"></div>
</div>
<svg class="w-16 h-px text-primary flex-shrink-0" fill="none" viewBox="0 0 64 1" xmlns="http://www.w3.org/2000/svg">
<path d="M0 0.5H64" stroke="currentColor" stroke-width="2"></path>
</svg>
<div class="w-40 bg-surface-light dark:bg-surface-dark rounded-lg shadow-md p-3 border border-border-light dark:border-border-dark relative flex-shrink-0">
<div class="flex items-center">
<span class="material-icons text-purple-500 text-base mr-2">http</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">HTTP Request</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark mt-1">Make a request.</p>
<div class="absolute -left-2 top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary border-2 border-surface-light dark:border-surface-dark"></div>
<div class="absolute -right-2 top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary border-2 border-surface-light dark:border-surface-dark"></div>
</div>
<svg class="w-16 h-px text-primary flex-shrink-0" fill="none" viewBox="0 0 64 1" xmlns="http://www.w3.org/2000/svg">
<path d="M0 0.5H64" stroke="currentColor" stroke-width="2"></path>
</svg>
<div class="w-40 bg-surface-light dark:bg-surface-dark rounded-lg shadow-md p-3 border border-border-light dark:border-border-dark relative flex-shrink-0">
<div class="flex items-center">
<span class="material-icons text-red-500 text-base mr-2">router</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">Openrouter</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark mt-1">Connect models.</p>
<div class="absolute -left-2 top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary border-2 border-surface-light dark:border-surface-dark"></div>
<div class="absolute -right-2 top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary border-2 border-surface-light dark:border-surface-dark"></div>
</div>
<svg class="w-16 h-px text-primary flex-shrink-0" fill="none" viewBox="0 0 64 1" xmlns="http://www.w3.org/2000/svg">
<path d="M0 0.5H64" stroke="currentColor" stroke-width="2"></path>
</svg>
<div class="w-40 bg-surface-light dark:bg-surface-dark rounded-lg shadow-md p-3 border border-border-light dark:border-border-dark relative flex-shrink-0">
<div class="flex items-center">
<span class="material-icons text-teal-500 text-base mr-2">save</span>
<h4 class="font-semibold text-sm text-text-light dark:text-text-dark">Save CSV</h4>
</div>
<p class="text-xs text-subtle-light dark:text-subtle-dark mt-1">Save data to file.</p>
<div class="absolute -left-2 top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-primary border-2 border-surface-light dark:border-surface-dark"></div>
</div>
</div>
</div>
</div>
</div>
<div class="w-80 bg-surface-light dark:bg-surface-dark rounded-lg border border-border-light dark:border-border-dark p-4 flex flex-col">
<h3 class="text-lg font-semibold mb-4 text-text-light dark:text-text-dark">Properties</h3>
<div class="space-y-4 flex-grow overflow-y-auto pr-2">
<div>
<label class="text-sm font-medium text-subtle-light dark:text-subtle-dark" for="node-name">Node Name</label>
<input class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-background-light dark:bg-background-dark shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-text-light dark:text-text-dark" id="node-name" type="text" value="GET Users"/>
</div>
<div>
<label class="text-sm font-medium text-subtle-light dark:text-subtle-dark" for="endpoint">Endpoint URL</label>
<input class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-background-light dark:bg-background-dark shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-text-light dark:text-text-dark" id="endpoint" type="text" value="/v1/users"/>
</div>
<div>
<label class="text-sm font-medium text-subtle-light dark:text-subtle-dark" for="method">Method</label>
<select class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-background-light dark:bg-background-dark shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-text-light dark:text-text-dark" id="method">
<option>GET</option>
<option>POST</option>
<option>PUT</option>
<option>DELETE</option>
</select>
</div>
<div>
<h4 class="text-sm font-medium text-subtle-light dark:text-subtle-dark mb-2">Headers</h4>
<div class="space-y-2">
<div class="flex items-center space-x-2">
<input class="w-1/2 rounded-md border-border-light dark:border-border-dark bg-background-light dark:bg-background-dark shadow-sm text-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-text-light dark:text-text-dark" placeholder="Key" type="text" value="Content-Type"/>
<input class="w-1/2 rounded-md border-border-light dark:border-border-dark bg-background-light dark:bg-background-dark shadow-sm text-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-text-light dark:text-text-dark" placeholder="Value" type="text" value="application/json"/>
<button class="text-subtle-light dark:text-subtle-dark hover:text-red-500"><span class="material-icons text-base">remove_circle_outline</span></button>
</div>
<div class="flex items-center space-x-2">
<input class="w-1/2 rounded-md border-border-light dark:border-border-dark bg-background-light dark:bg-background-dark shadow-sm text-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-text-light dark:text-text-dark" placeholder="Key" type="text" value="Authorization"/>
<input class="w-1/2 rounded-md border-border-light dark:border-border-dark bg-background-light dark:bg-background-dark shadow-sm text-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-text-light dark:text-text-dark" placeholder="Value" type="text" value="Bearer ..."/>
<button class="text-subtle-light dark:text-subtle-dark hover:text-red-500"><span class="material-icons text-base">remove_circle_outline</span></button>
</div>
<button class="text-sm font-medium text-primary hover:underline">Add Header</button>
</div>
</div>
<div>
<h4 class="text-sm font-medium text-subtle-light dark:text-subtle-dark mb-2">Assertions</h4>
<div class="p-3 bg-background-light dark:bg-background-dark rounded-md border border-border-light dark:border-border-dark space-y-2">
<p class="text-xs">Assert <span class="font-mono bg-gray-200 dark:bg-gray-700 p-1 rounded">status_code</span> is <span class="font-mono bg-gray-200 dark:bg-gray-700 p-1 rounded">200</span></p>
<p class="text-xs">Assert <span class="font-mono bg-gray-200 dark:bg-gray-700 p-1 rounded">response.body.length</span> &gt; <span class="font-mono bg-gray-200 dark:bg-gray-700 p-1 rounded">0</span></p>
</div>
<button class="text-sm font-medium text-primary hover:underline mt-2">Add Assertion</button>
</div>
</div>
</div>
</div>
</main>
</div>
<style>
        .bg-dots-pattern {
            background-color: var(--tw-bg-opacity,1) var(--tw-bg-color, theme('colors.background.light'));
            background-image: radial-gradient(theme('colors.border.light') 1px, transparent 1px);
            background-size: 16px 16px;
        }
        .dark .bg-dots-pattern {
             background-color: var(--tw-bg-opacity,1) var(--tw-bg-color, theme('colors.background.dark'));
            background-image: radial-gradient(theme('colors.border.dark') 1px, transparent 1px);
        }
    </style>

</body></html>