<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>AI QA TEST</title>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<script>
    tailwind.config = {
      darkMode: "class",
      theme: {
        extend: {
          colors: {
            primary: "#6366F1",
            "background-light": "#F8F9FA",
            "background-dark": "#1A1A1A",
            "card-light": "#FFFFFF",
            "card-dark": "#242424",
            "text-light": "#1F2937",
            "text-dark": "#F3F4F6",
            "subtext-light": "#6B7280",
            "subtext-dark": "#9CA3AF",
            "border-light": "#E5E7EB",
            "border-dark": "#374151"
          },
          fontFamily: {
            display: ["Poppins", "sans-serif"],
          },
          borderRadius: {
            DEFAULT: "12px",
          },
        },
      },
    };
  </script>
<style>
    .material-icons-outlined {
      font-size: inherit;
    }
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="font-display bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark transition-colors duration-300">
<div class="flex h-screen">
<aside class="w-16 md:w-64 bg-card-light dark:bg-card-dark flex flex-col transition-all duration-300 border-r border-border-light dark:border-border-dark">
<div class="flex items-center justify-center h-16 border-b border-border-light dark:border-border-dark">
<span class="material-icons-outlined text-3xl text-primary">auto_awesome</span>
<h1 class="text-xl font-bold ml-2 hidden md:block">AI QA</h1>
</div>
<nav class="flex-1 overflow-y-auto">
<div class="px-2 py-4 md:px-4">
<h2 class="text-xs font-semibold text-subtext-light dark:text-subtext-dark uppercase tracking-wider hidden md:block">Projects</h2>
<ul class="mt-3 space-y-1">
<li>
<a class="flex items-center p-2 md:p-3 text-subtext-light dark:text-subtext-dark rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group" href="#">
<span class="material-icons-outlined">folder</span>
<span class="ml-3 hidden md:block">Project Alpha</span>
</a>
</li>
<li>
<a class="flex items-center p-2 md:p-3 text-subtext-light dark:text-subtext-dark rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group" href="#">
<span class="material-icons-outlined">folder</span>
<span class="ml-3 hidden md:block">Project Beta</span>
</a>
</li>
<li>
<a class="flex items-center p-2 md:p-3 text-subtext-light dark:text-subtext-dark rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group" href="#">
<span class="material-icons-outlined">folder_open</span>
<span class="ml-3 hidden md:block text-primary font-semibold">Project Gamma</span>
</a>
</li>
</ul>
</div>
</nav>
<div class="p-2 md:p-4 border-t border-border-light dark:border-border-dark">
<a href="/projectcreation.html" class="w-full flex items-center justify-center md:justify-start p-2 md:p-3 bg-primary text-white font-semibold rounded-lg shadow-lg hover:bg-indigo-700 transition-colors duration-300">
<span class="material-icons-outlined">add</span>
<span class="ml-2 hidden md:block">New Project</span>
</a>
</div>
</aside>
<div class="flex-1 flex flex-col">
<header class="h-16 flex items-center justify-between px-6 border-b border-border-light dark:border-border-dark bg-card-light dark:bg-card-dark">
<div class="flex items-center space-x-4">
<button class="p-2 -ml-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 md:hidden">
<span class="material-icons-outlined">menu</span>
</butt>
<h1 class="text-xl font-semibold">Welcome</h1>
</div>
<div class="flex items-center space-x-4">
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800">
<span class="material-icons-outlined">search</span>
</button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800">
<span class="material-icons-outlined">notifications</span>
</button>
<img alt="User avatar" class="w-10 h-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBAda3oh7tegTDZM-W67wm3OBy_LSIzvIQvxZXRxFCXi0bCCkNt2dQMFDnb4msDCNPkAhLXhQBFK1-B3p0fvoqVNLYxw0mhrpYAsyJyJXgenjdbWw1dk8uy8BpZ0e1YM9zQPQ8XG_h98Kt_oJWit54kzM3LU3Okb3STR4j30eUp3IRSV993JJjLVMSDOLTkqHQhGfNUMfuHkPOHvFiOqelo441USfdbgKAHd1xl-JeRgYzA745ezwti8mr4vMB3OHpFbq8ILyQS4lok"/>
</div>
</header>
<main class="flex-1 flex flex-col items-center justify-center p-6 text-center bg-background-light dark:bg-background-dark">
<div class="w-24 h-24 bg-primary rounded-3xl flex items-center justify-center mb-6 shadow-lg">
<span class="text-white text-5xl font-bold">AI</span>
</div>
<h2 class="text-3xl font-bold text-text-light dark:text-text-dark">Welcome to AI QA TEST</h2>
<p class="mt-2 text-lg text-subtext-light dark:text-subtext-dark max-w-md mx-auto">Intelligent automated testing for modern applications. Select a project or create a new one to get started.</p>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12 w-full max-w-3xl">
<div class="bg-card-light dark:bg-card-dark p-8 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 flex flex-col items-center">
<div class="w-16 h-16 bg-indigo-100 dark:bg-indigo-900/50 rounded-full flex items-center justify-center mb-4">
<span class="material-icons-outlined text-3xl text-primary">api</span>
</div>
<h3 class="text-xl font-semibold text-text-light dark:text-text-dark">API Testing</h3>
<p class="mt-2 text-subtext-light dark:text-subtext-dark">Automated REST API testing with intelligent validation.</p>
</div>
<div class="bg-card-light dark:bg-card-dark p-8 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 flex flex-col items-center">
<div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mb-4">
<span class="material-icons-outlined text-3xl text-purple-500">devices</span>
</div>
<h3 class="text-xl font-semibold text-text-light dark:text-text-dark">UI Testing</h3>
<p class="mt-2 text-subtext-light dark:text-subtext-dark">Comprehensive user interface and workflow testing.</p>
</div>
</div>
</main>
</div>
</div>

</body></html>