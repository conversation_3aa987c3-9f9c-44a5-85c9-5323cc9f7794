<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>API Test Page</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: "#6366F1",
                        "background-light": "#F8F9FA",
                        "background-dark": "#111827",
                        "surface-light": "#FFFFFF",
                        "surface-dark": "#1F2937",
                        "text-light": "#111827",
                        "text-dark": "#F9FAFB",
                        "subtext-light": "#6B7280",
                        "subtext-dark": "#9CA3AF",
                        "border-light": "#E5E7EB",
                        "border-dark": "#374151",
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"],
                    },
                    borderRadius: {
                        DEFAULT: "0.5rem",
                    },
                },
            },
        };
    </script>
<style>
        .material-icons {
            font-size: 20px;
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-sans text-text-light dark:text-text-dark">
<div class="flex h-screen">
<aside class="w-64 bg-surface-light dark:bg-surface-dark flex flex-col border-r border-border-light dark:border-border-dark">
<div class="flex items-center justify-start p-4 h-16 border-b border-border-light dark:border-border-dark">
<div class="bg-primary p-2 rounded-lg mr-3">
<span class="material-icons text-white">auto_awesome</span>
</div>
<h1 class="text-xl font-bold text-text-light dark:text-text-dark">AI QA</h1>
</div>
<div class="flex-grow p-4">
<h2 class="text-xs font-semibold text-subtle-light dark:text-subtle-dark uppercase tracking-wider mb-3">Projects</h2>
<nav class="space-y-1">
<a class="flex items-center px-3 py-2 text-sm font-medium rounded text-text-light dark:text-text-dark hover:bg-gray-100 dark:hover:bg-gray-700" href="#">
<span class="material-icons text-subtle-light dark:text-subtle-dark mr-3">folder</span>
<span class="truncate">Project Alpha</span>
</a>
<a class="flex items-center px-3 py-2 text-sm font-medium rounded text-text-light dark:text-text-dark hover:bg-gray-100 dark:hover:bg-gray-700" href="#">
<span class="material-icons text-subtle-light dark:text-subtle-dark mr-3">folder</span>
<span class="truncate">Project Beta</span>
</a>
<a class="flex items-center px-3 py-2 text-sm font-medium rounded bg-primary/10 dark:bg-primary/20 text-primary" href="#">
<span class="material-icons text-primary mr-3">folder</span>
<span class="truncate">Project Gamma</span>
</a>
</nav>
</div>
<div class="p-4 border-t border-border-light dark:border-border-dark">
<button class="w-full flex items-center justify-center bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors">
<span class="material-icons mr-2">add</span>
                    New Project
                </button>
</div>
</aside>
<main class="flex-1 flex flex-col">
<header class="flex items-center justify-end p-4 h-16 border-b border-border-light dark:border-border-dark bg-surface-light dark:bg-surface-dark">
<div class="flex items-center space-x-6">
<button class="text-subtle-light dark:text-subtle-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">help_outline</span>
</button>
<button class="relative text-subtle-light dark:text-subtle-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">notifications</span>
<span class="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full"></span>
</button>
<button>
<img alt="User avatar" class="rounded-full w-8 h-8" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDAJQxVRAYQA-ltZ4P0iK0WT_c65QonQKQ5gQmJ995sHSOLBCHSO4URrMk8awRjTxfttJ4IIRjo6e579d40DtFcIqG0VA81WNRkK6462JFTHZMtx_tZaEnJx5PcOwabM524ZyJw0_Achg1s_X33Gzcwuj5utOAFo4xcW_zegjpy_cpqk0Xo0cMo0qABYGl8gIw1UiSg2aXIovLLmFO5ifrkRFeipRV6L5h88hgE96xLgiEEVvTeiEBMbJ8GvgH5SU0R7I3wmVT7fyU"/>
</button>
</div>
</header>
<div class="px-6 bg-surface-light dark:bg-surface-dark border-b border-border-light dark:border-border-dark">
<div class="flex space-x-8">
<a href="/apitest.html" class="py-4 px-1 border-b-2 border-primary text-primary font-semibold text-sm" href="#">API Test</a>
<a href="/apitestflow.html" class="py-4 px-1 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-600 hover:text-text-light dark:hover:text-text-dark font-medium text-sm" href="#">API Flow Test</a>
<a href="/integration.html" class="py-4 px-1 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-600 hover:text-text-light dark:hover:text-text-dark font-medium text-sm" href="#">Integration</a>
<a class="py-4 px-1 border-b-2 border-transparent text-subtext-light dark:text-subtext-dark hover:border-gray-300 dark:hover:border-gray-600 hover:text-text-light dark:hover:text-text-dark font-medium text-sm" href="#">Results</a>
</div>
</div>
<div class="flex-1 p-8 overflow-y-auto">
<div class="bg-surface-light dark:bg-surface-dark p-6 rounded-lg shadow-sm">
<div class="flex items-center justify-between mb-6">
<div class="flex items-center space-x-4">
<div class="relative">
<span class="material-icons absolute left-3 top-1/2 -translate-y-1/2 text-subtext-light dark:text-subtext-dark">search</span>
<input class="pl-10 pr-4 py-2 w-72 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:ring-primary focus:border-primary" placeholder="Search tests..." type="text"/>
</div>
<div>
<select class="py-2 pl-3 pr-8 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md focus:ring-primary focus:border-primary">
<option>Filter by method</option>
<option>GET</option>
<option>POST</option>
<option>PUT</option>
<option>DELETE</option>
</select>
</div>
</div>
<div class="flex items-center space-x-4">
<div class="flex items-center space-x-2 text-sm">
<span class="material-icons text-subtext-light dark:text-subtext-dark">bolt</span>
<span class="text-subtext-light dark:text-subtext-dark">Rate Limit:</span>
<input class="w-20 px-2 py-1 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md text-sm focus:ring-primary focus:border-primary" type="number" value="100"/>
<span class="text-subtext-light dark:text-subtext-dark">/min</span>
</div>
<div class="flex items-center space-x-2 text-sm">
<span class="material-icons text-subtext-light dark:text-subtext-dark">timer</span>
<span class="text-subtext-light dark:text-subtext-dark">Timeout:</span>
<input class="w-20 px-2 py-1 bg-background-light dark:bg-background-dark border border-border-light dark:border-border-dark rounded-md text-sm focus:ring-primary focus:border-primary" type="number" value="30"/>
<span class="text-subtext-light dark:text-subtext-dark">s</span>
</div>
<button class="bg-primary text-white py-2 px-4 rounded-md flex items-center space-x-2 text-sm font-medium hover:bg-primary/90">
<span class="material-icons">play_arrow</span>
<span>Run All</span>
</button>
</div>
</div>
<div class="overflow-x-auto">
<table class="w-full text-sm text-left">
<thead class="text-xs text-subtext-light dark:text-subtext-dark uppercase bg-background-light dark:bg-background-dark">
<tr>
<th class="px-6 py-3" scope="col">Endpoint</th>
<th class="px-6 py-3" scope="col">Method</th>
<th class="px-6 py-3" scope="col">Status</th>
<th class="px-6 py-3 text-right" scope="col">Actions</th>
</tr>
</thead>
<tbody>
<tr
  class="bg-surface-light dark:bg-surface-dark border-b border-border-light dark:border-border-dark hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer"
  onclick="if (!event.target.closest('a')) window.location='/detailedapitest.html';"
>
  <td class="px-6 py-4 font-medium whitespace-nowrap">
    <a href="/detailedapitest.html" class="text-blue-600 hover:underline">
      /v1/users
    </a>
  </td>

  <td class="px-6 py-4">
    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">GET</span>
  </td>

  <td class="px-6 py-4">
    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
      <span class="w-2 h-2 mr-1 bg-green-500 rounded-full"></span>
      200 OK
    </span>
  </td>

  <td class="px-6 py-4 text-right">
    <div class="flex items-center justify-end space-x-2">
      <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark">
        <span class="material-icons" title="Documentation">description</span>
      </button>
      <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark">
        <span class="material-icons" title="Request Body">code</span>
      </button>
      <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark">
        <span class="material-icons" title="Header">add_box</span>
      </button>
      <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark">
        <span class="material-icons" title="Expected Result">check_circle_outline</span>
      </button>
      <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark">
        <span class="material-icons" title="History">history</span>
      </button>
      <button class="p-2 rounded-full bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-400 dark:hover:bg-green-900">
        <span class="material-icons">play_arrow</span>
      </button>
    </div>
  </td>
</tr>

<tr class="bg-surface-light dark:bg-surface-dark border-b border-border-light dark:border-border-dark hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer">
<td class="px-6 py-4 font-medium whitespace-nowrap">/v1/users</td>
<td class="px-6 py-4">
<span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">POST</span>
</td>
<td class="px-6 py-4">
<span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
<span class="w-2 h-2 mr-1 bg-green-500 rounded-full"></span>
                                                201 Created
                                            </span>
</td>
<td class="px-6 py-4 text-right">
<div class="flex items-center justify-end space-x-2">
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">description</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">code</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">add_box</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">check_circle_outline</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">history</span></button>
<button class="p-2 rounded-full bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-400 dark:hover:bg-green-900"><span class="material-icons">play_arrow</span></button>
</div>
</td>
</tr>
<tr class="bg-surface-light dark:bg-surface-dark border-b border-border-light dark:border-border-dark hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer">
<td class="px-6 py-4 font-medium whitespace-nowrap">/v1/users/{id}</td>
<td class="px-6 py-4">
<span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">PUT</span>
</td>
<td class="px-6 py-4">
<span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
<span class="w-2 h-2 mr-1 bg-red-500 rounded-full"></span>
                                                400 Bad Request
                                            </span>
</td>
<td class="px-6 py-4 text-right">
<div class="flex items-center justify-end space-x-2">
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">description</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">code</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">add_box</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">check_circle_outline</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">history</span></button>
<button class="p-2 rounded-full bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-400 dark:hover:bg-green-900"><span class="material-icons">play_arrow</span></button>
</div>
</td>
</tr>
<tr class="bg-surface-light dark:bg-surface-dark hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer">
<td class="px-6 py-4 font-medium whitespace-nowrap">/v1/users/{id}</td>
<td class="px-6 py-4">
<span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">DELETE</span>
</td>
<td class="px-6 py-4">
<span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
<span class="w-2 h-2 mr-1 bg-gray-400 rounded-full"></span>
                                                Not Run
                                            </span>
</td>
<td class="px-6 py-4 text-right">
<div class="flex items-center justify-end space-x-2">
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">description</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">code</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">add_box</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">check_circle_outline</span></button>
<button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-subtext-light dark:text-subtext-dark"><span class="material-icons">history</span></button>
<button class="p-2 rounded-full bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-400 dark:hover:bg-green-900"><span class="material-icons">play_arrow</span></button>
</div>
</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
</main>
</div>
</div>
</body></html>