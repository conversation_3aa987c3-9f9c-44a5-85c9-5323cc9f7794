<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>AI QA - Integration</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: "#6366F1",
                        "background-light": "#F7F8FC",
                        "background-dark": "#121212",
                        "card-light": "#FFFFFF",
                        "card-dark": "#1E1E1E",
                        "text-light": "#111827",
                        "text-dark": "#F3F4F6",
                        "subtext-light": "#6B7280",
                        "subtext-dark": "#9CA3AF",
                        "border-light": "#E5E7EB",
                        "border-dark": "#374151",
                        success: "#10B981",
                        warning: "#F59E0B"
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"],
                    },
                    borderRadius: {
                        DEFAULT: "0.5rem",
                        lg: "0.75rem",
                        xl: "1rem"
                    },
                },
            },
        };
    </script>
<style>
        .material-icons {
            font-size: 20px;
        }
        .modal-overlay {
            display: none;
        }
        .modal-overlay.active {
            display: flex;
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-sans text-text-light dark:text-text-dark">
<div class="flex h-screen">
<aside class="w-64 bg-card-light dark:bg-card-dark flex flex-col border-r border-border-light dark:border-border-dark">
<div class="flex items-center justify-start h-16 px-6 border-b border-border-light dark:border-border-dark">
<div class="flex items-center space-x-3">
<div class="bg-primary p-2 rounded-lg">
<span class="material-icons text-white">auto_awesome</span>
</div>
<span class="font-bold text-xl text-text-light dark:text-text-dark">AI QA</span>
</div>
</div>
<div class="flex-1 p-4 space-y-4">
<h3 class="px-4 text-sm font-semibold text-subtext-light dark:text-subtext-dark uppercase tracking-wider">Projects</h3>
<nav class="space-y-1">
<a class="flex items-center px-4 py-2 text-sm font-medium text-subtext-light dark:text-subtext-dark hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md" href="#">
<span class="material-icons mr-3">folder</span> Project Alpha
                    </a>
<a class="flex items-center px-4 py-2 text-sm font-medium text-subtext-light dark:text-subtext-dark hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md" href="#">
<span class="material-icons mr-3">folder</span> Project Beta
                    </a>
<a class="flex items-center px-4 py-2 text-sm font-medium bg-primary/10 text-primary rounded-md" href="#">
<span class="material-icons mr-3">folder</span> Project Gamma
                    </a>
</nav>
</div>
<div class="p-4 border-t border-border-light dark:border-border-dark">
<button class="w-full flex items-center justify-center bg-primary text-white py-2.5 rounded-md hover:bg-primary/90 transition-colors">
<span class="material-icons mr-2">add</span> New Project
                </button>
</div>
</aside>
<main class="flex-1 flex flex-col">
<header class="flex items-center justify-between h-16 px-8 border-b border-border-light dark:border-border-dark bg-card-light dark:bg-card-dark">
<h1 class="text-xl font-semibold text-text-light dark:text-text-dark">Integration</h1>
<div class="flex items-center space-x-4">
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">search</span>
</button>
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark">
<span class="material-icons">notifications_none</span>
</button>
<div class="w-8 h-8 bg-gray-300 rounded-full">
<img alt="User avatar" class="rounded-full w-full h-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDkfo7Zcsl9mq9UDFYXSLKtI2oNKM0R_Kcwq32_KI1h99c_Js8eFd_e0tjm6K7e7yn5meC6x8dw3AgOIrxA5etVZt3-DsKvRBapy4QH_ctxMsoBSG5MyyKTGNUuRcy77aQsct58e3ygzN6PrcZfLzegVF05x5vMoUorZ0Jy1sVq6nNToBL_0kVCHHgmlWgHjvqpd1X5Lo0D7uSls1dLurmZ7cU-tAt9BDhdrcHoe2IpSReZoFd0Yz9fFxLZPiuNeWkBeWGAqQbol-A"/>
</div>
</div>
</header>
<nav class="bg-card-light dark:bg-card-dark border-b border-border-light dark:border-border-dark">
<div class="px-8 flex space-x-8">
<a href="/apitest.html" class="py-3 text-sm font-medium text-subtext-light dark:text-subtext-dark hover:text-primary border-b-2 border-transparent hover:border-primary" href="#">API Test</a>
<a href="/apitestflow.html" class="py-3 text-sm font-medium text-subtext-light dark:text-subtext-dark hover:text-primary border-b-2 border-transparent hover:border-primary" href="#">API Flow Test</a>
<a href="/integration.html" class="py-3 text-sm font-medium text-primary border-b-2 border-primary" href="#">Integration</a>
<a class="py-3 text-sm font-medium text-subtext-light dark:text-subtext-dark hover:text-primary border-b-2 border-transparent hover:border-primary" href="#">Results</a>
</div>
</nav>
<div class="flex-1 p-8 bg-background-light dark:bg-background-dark overflow-y-auto">
<div class="bg-card-light dark:bg-card-dark rounded-xl shadow-sm p-6">
<div class="flex justify-between items-center mb-6">
<h2 class="text-xl font-bold text-text-light dark:text-text-dark">Connected Integrations</h2>
<button class="bg-primary text-white font-medium py-2 px-4 rounded-md hover:bg-primary/90 transition-colors flex items-center" id="add-new-btn">
<span class="material-icons mr-2 text-base">add</span> Add New
                        </button>
</div>
<div class="overflow-hidden">
<ul class="divide-y divide-border-light dark:divide-border-dark -mx-6">
<li class="px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
<div class="flex items-center">
<img alt="Figma logo" class="w-8 h-8 mr-4" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBbpByDwrpnLq9A3LXOXRdOgo_B4NZayG-o-8NhuzsEhjJH5ZyiGZ8u7VqK9SCwtziHbGEkbuh_tHwrt99jbMExfKfLLTCPVsbeyRh0etq-l2ObSYWN4Z_oBaqruMGUjV975YEHx0d3IEn0fh6A2fA7qPQNvayXnur2FqPQlgKKu_uGVBjN1o3hRMvqr4C5cWy7jAePVPUocc6O3vtb6rIzkB_P0WUdUTEInnQ7Kjqkkn_rYJZxBZMlZO4Qa_iDXdStYd0xOLtGw3E"/>
<div>
<p class="font-medium text-sm text-text-light dark:text-text-dark">Design System</p>
<p class="text-sm text-subtext-light dark:text-subtext-dark">Figma</p>
</div>
</div>
<div class="flex items-center space-x-4">
<div class="flex items-center text-success">
<span class="material-icons mr-1 text-sm">check_circle</span>
<span class="text-sm font-medium">Connected</span>
</div>
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark"><span class="material-icons">more_horiz</span></button>
</div>
</li>
<li class="px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
<div class="flex items-center">
<img alt="Git logo" class="w-8 h-8 mr-4" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAXoxiNEt_41_HBWcesQSQ9wxapkFOnA78cd_xadyhbCre1abDd3NBxEOTPcyzfQ3d3gQA6xGh2JN-_DdiM-dAn5cGZRHnLDg4FxGvBpmSA40uzdh_yVzqnYkS96prqYljpXDdZa3XcFv23RS_-4aCLB7b9s_YGE1niq8b26yQNMCsuhS1Dc8ICSQCwlWtpUxuYnCIzgjw0OXButO_1Dz7ZS92CkHeVj9rq7d6RyHLvns1dJUeQmeeiQUpX-2B132EmwVNnwWjzRS8"/>
<div>
<p class="font-medium text-sm text-text-light dark:text-text-dark">Frontend Repository</p>
<p class="text-sm text-subtext-light dark:text-subtext-dark">Github</p>
</div>
</div>
<div class="flex items-center space-x-4">
<div class="flex items-center text-success">
<span class="material-icons mr-1 text-sm">check_circle</span>
<span class="text-sm font-medium">Connected</span>
</div>
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark"><span class="material-icons">more_horiz</span></button>
</div>
</li>
<li class="px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
<div class="flex items-center">
<img alt="Jira logo" class="w-8 h-8 mr-4" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDFu1PCifBzJ2veYUQDPVAXtJ66oVDIzh6RjuoaDeGm7oGKrF24atGvkkqrjwZLq6O3QQbYJYpowYMkzqKyhB4N76j92FAA8w4MOIE-NcmFlo1TnpHSetRC_fgKRbOdQefNJp23NMijBNdVEYgKUN7mzwipCGruB5Wp9hLPSPkofPbgE977xkzAEqFn9OfWsOlZhKCQXv8Z6NAvmqGSoSeaavkAyQ1RJcmdTHyL2qv3_NCc6tqkeC9fwHoNdt3glve9z5sE_NSHk2M"/>
<div>
<p class="font-medium text-sm text-text-light dark:text-text-dark">Project Tickets</p>
<p class="text-sm text-subtext-light dark:text-subtext-dark">Jira</p>
</div>
</div>
<div class="flex items-center space-x-4">
<div class="flex items-center text-warning">
<span class="material-icons mr-1 text-sm">warning</span>
<span class="text-sm font-medium">Needs Attention</span>
</div>
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark"><span class="material-icons">more_horiz</span></button>
</div>
</li>
</ul>
</div>
</div>
</div>
</main>
</div>
<div class="modal-overlay fixed inset-0 bg-black bg-opacity-50 items-center justify-center p-4" id="integration-modal">
<div class="bg-card-light dark:bg-card-dark rounded-xl shadow-xl w-full max-w-lg">
<div class="p-6 border-b border-border-light dark:border-border-dark flex justify-between items-center">
<h3 class="text-xl font-semibold">Select Integration Type</h3>
<button class="text-subtext-light dark:text-subtext-dark hover:text-text-light dark:hover:text-text-dark" id="close-modal-btn">
<span class="material-icons">close</span>
</button>
</div>
<div class="p-6">
<div class="grid grid-cols-3 sm:grid-cols-4 gap-4 mb-6">
<button class="flex flex-col items-center justify-center p-3 border border-border-light dark:border-border-dark rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hover:border-primary dark:hover:border-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary">
<img alt="GitHub logo" class="w-10 h-10 mb-2" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBKNQTAZW-yzvn8FIpXWiTrvOwbwbjLzlEdAn0jkpjEyOHgOG2d8mVXdhCjut5Lxg1EoOG3MIQ0XCUcht8FENk-qvCD07LmFDQuGpFOFB7G_mfQFi-l3I8EJ--Kf1H0kaKpviYUOU2sOhyKVYKP10pLsLU5tWr-Cahb3HEVGw2sENEqst-OCK9hkNIkpyQTQpJo18N8niZQrpR7lH9DIhyJicroF7597_U1bLYbDqB8FnCYy8ukNuRzGorCwL9ovNW2dwRPOUbKNFg"/>
<span class="text-sm font-medium">GitHub</span>
</button>
<button class="flex flex-col items-center justify-center p-3 border border-border-light dark:border-border-dark rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hover:border-primary dark:hover:border-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary">
<img alt="Jira logo" class="w-10 h-10 mb-2" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAT0hDs8Uf14leHdD4V-lr95Du4dq9l3enBiumCgwTWMttsIFb3t8__WzfkYJH1z_K7TuzHqdtIrINIEnhLCEBI0ME_7SkF7P10HgQagfuZA17QrmhmBcFDNhhNR0e6rBv4T3-FKzetIs8PECjF70ZOPWy6z-_FwwD1dlxMqzv7tYCHNfJjdaDM0QJCkAfWZWaiAFLUbkGr1w4bg3TREiny72u7IwxQ8aJEn_BV-tm2_wf519Ct3sGH7o_WkBIJzE6_bKkgt0-9cUA"/>
<span class="text-sm font-medium">Jira</span>
</button>
<button class="flex flex-col items-center justify-center p-3 border border-border-light dark:border-border-dark rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hover:border-primary dark:hover:border-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary">
<img alt="Figma logo" class="w-10 h-10 mb-2" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCcz7CVjwPoSeyDGrVcAMRnkNfKABUiM6LID_rEIJKLww1CEBRH1muPAHxIYU_95zVb_9bQ9HNYzKQ7603ssrLdNv3bj07Royl6JXIKvcWpMZyTa7SKsPGln-kTgBguauhRtQO0mfEQXNxXGQhbtiAEwEHeB_yJxBA211KOVwKPa1ZwcJTa4x7wchQTpYpRY53jgBnJGm2o9pAP3iWx9p4JMkpepV8osvT6mLGUABB2zGw7Qd0jCO5v4JAaJRZ-dxcUnveWfXm9AUs"/>
<span class="text-sm font-medium">Figma</span>
</button>
</div>
<div>
<label class="block text-sm font-medium text-text-light dark:text-text-dark mb-2" for="modal-repo-url">Repository URL</label>
<div class="relative">
<input class="w-full pl-4 pr-4 py-2.5 bg-gray-50 dark:bg-gray-800 border border-border-light dark:border-border-dark rounded-md focus:ring-primary focus:border-primary text-text-light dark:text-text-dark placeholder-subtext-light dark:placeholder-subtext-dark" id="modal-repo-url" name="repo-url" placeholder="e.g., https://github.com/user/repo" type="url"/>
</div>
</div>
</div>
<div class="p-6 bg-background-light dark:bg-background-dark/50 rounded-b-xl flex justify-end space-x-4">
<button class="bg-card-light dark:bg-card-dark border border-border-light dark:border-border-dark text-text-light dark:text-text-dark font-medium py-2.5 px-6 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" id="cancel-btn">Cancel</button>
<button class="bg-primary text-white font-medium py-2.5 px-6 rounded-md hover:bg-primary/90 transition-colors flex items-center">
<span>Connect</span>
</button>
</div>
</div>
</div>
<script>
        const addNewBtn = document.getElementById('add-new-btn');
        const modal = document.getElementById('integration-modal');
        const closeModalBtn = document.getElementById('close-modal-btn');
        const cancelBtn = document.getElementById('cancel-btn');
        function toggleModal() {
            modal.classList.toggle('active');
        }
        if (addNewBtn) {
            addNewBtn.addEventListener('click', toggleModal);
        }
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', toggleModal);
        }
        if (cancelBtn) {
            cancelBtn.addEventListener('click', toggleModal);
        }
        // Close modal if clicking outside of it
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                toggleModal();
            }
        });
    </script>

</body></html>